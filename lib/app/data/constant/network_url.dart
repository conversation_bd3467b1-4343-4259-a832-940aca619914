import 'package:flutter_dotenv/flutter_dotenv.dart';

Future<void> initializeEnvironment() async {
  await dotenv.load();
}

String baseApiUrl = dotenv.env['BASE_API_URL'] ?? '';
String baseApiMpUrl = dotenv.env['BASE_API_URL_MP'] ?? '';
String baseSocketUrl = dotenv.env['SOCKET_URL'] ?? '';
String baseWSUrl = dotenv.env['WS_URL'] ?? '';

String urlLogin = '/auth/login';
String urlQuizFilter = '/quiz';
String urlStartQuiz = '/quiz/start';
String urlQuizQuestion = '/quiz/question';
String urlSubmitQuestion = '/quiz/answer';
String urlFinishQuiz = '/quiz/finish';
String urlQuizProgress = '/quiz/progress';
String urlQuizReview = '/quiz/review';
String urlAssignment = '/assignment';
String urlUploadAssignment = '/assignment-answer';
String urlAttendance = '/attendance';
String urlChatHistory = '/chat/history';
String urlChatUserById = '/chat/list-chat';
String urlSearchUserChat = '/chat/search-user';
String urlProfile = '/user';
String urlChangrPassword = '/user/change-password';
String urlPersonalProfile = '/user/personal-profile';
String urlGeneralEducation = '/user/general-educations';
String urlMilitaryEducation = '/user/military-educations';
String urlCourse = '/user/courses';
String urlLanguageSpoken = '/user/language-spokens';
String urlAttachment = '/user/attachments';
String urlFamilyInformation = '/user/family-information';
String urlSchedule = '/detail-schedule/monthly';
String urlAccumulatedGradesScore = '/gradebook';
String urlAcademicGrades = '/gradebook/academic';
String urlPersonalityGrades = '/gradebook/personality';
String urlSamaptaGrades = '/gradebook/jasmani';
String urlEvent = '/calendar-event';
String urlFeedback = '/feedback';

// Media Player
String urlUpdateLastLogin = '/user/login';
String urlVod = '/vod';
String urlVodPlayback = '/vod-playback';
String urlMyVod = '/vod/mine';
String urlUserInfo = '/auth/me';
String urlPlaylist = '/playlist';
String urlClip = '/clip';
String urlSearchLogs = '/log-search';
String urlPlayback = '/vod-playback';
String urlClipCollection = '/collection';
String urlVodRattings = '/vod-rating';
String urlBroadcast = '/broadcast';
String urlVodComment = '/vod-comment';
