import 'dart:async';

import 'package:flutter/material.dart';
import 'package:chewie/chewie.dart';
import 'package:get/get.dart';
import 'package:mides_skadik/app/data/models/response/media_player/broadcast/comment_model.dart';
import 'package:mides_skadik/app/data/services/media_player/broadcast/broadcast_service.dart';
import 'package:mides_skadik/app/data/services/media_player/dashboard/vod_service.dart';
import 'package:mides_skadik/app/data/services/media_player/playlist/vod_playback_service.dart';
import 'package:mides_skadik/app/data/utils/log_service.dart';
import 'package:video_player/video_player.dart';
import 'package:webview_flutter/webview_flutter.dart';
import 'package:youtube_player_flutter/youtube_player_flutter.dart';
import 'dart:convert';
import 'package:web_socket_channel/web_socket_channel.dart';
import 'package:web_socket_channel/io.dart';

class VideoController extends GetxController with WidgetsBindingObserver {
  var isPlaying = false.obs;
  var isBuffering = false.obs;
  var isSettingUp = false.obs;
  var videoUrl = ''.obs;
  var videoId = ''.obs;
  var lastWatchTime = const Duration(hours: 0, minutes: 0, seconds: 0).obs;
  var videoSourceUrl = ''.obs;
  var isInitialized = false.obs;
  var isControllerClosed = false.obs;
  late VideoPlayerController videoPlayerController;
  late ChewieController chewieController;
  late YoutubePlayerController youtubeController;
  late WebViewController webViewController;
  WebSocketChannel? webSocketChannel;

  final RxList<CommentModel> comments = <CommentModel>[].obs;
  var rattings = 0.0.obs;

  int lastSentSec = -1;
  VodService vodService = VodService();
  Timer? timer;
  // Static variable untuk track WebView instance
  static bool _webViewInUse = false;
  static WebViewController? _currentWebViewController;

  @override
  void onInit() {
    super.onInit();
    WidgetsBinding.instance.addObserver(this);
    initVideo();
    getComments();
    // Connect to WebSocket for real-time features
    connectToSocket();
  }

  @override
  void onClose() {
    isControllerClosed.value = true;
    stopTimerPeriodic();
    disconnectSocket();
    WidgetsBinding.instance.removeObserver(this);
    // LogService.log.i('🔄 VideoController onClose() started');
    // isControllerClosed.value = true;
    // WidgetsBinding.instance.removeObserver(this);

    // try {
    //   // Handle WebView disposal for YouTube videos
    //   if (videoUrl.value.contains('youtube.com')) {
    //     LogService.log.i('🌐 Disposing WebView for YouTube video');
    //     await _disposeWebViewSafely();
    //   }

    //   // Handle regular video player disposal
    //   if (isInitialized.value &&
    //       videoUrl.value.isNotEmpty &&
    //       !videoUrl.value.contains('youtube.com')) {
    //     LogService.log.i('Disposing regular video player');
    //     if (videoPlayerController.value.isInitialized) {
    //       videoPlayerController.pause();
    //       videoPlayerController.dispose();
    //     }
    //     chewieController.dispose();
    //   }

    //   // Reset all values
    //   isPlaying.value = false;
    //   isBuffering.value = false;
    //   isInitialized.value = false;
    //   videoUrl.value = '';
    //   videoId.value = '';
    //   videoSourceUrl.value = '';
    // } catch (e) {
    //   LogService.log.e('Error disposing video controller: $e');
    // }

    super.onClose();
  }

  void connectToSocket() {
    try {
      LogService.log.i('🔌 Attempting to connect to WebSocket...');

      // Connect to WebSocket server using WebSocket Channel
      webSocketChannel = IOWebSocketChannel.connect(
        Uri.parse('ws://93.127.185.148:3000/api/ws'),
      );

      LogService.log.i('✅ WebSocket connection established');

      // Send initial join message
      _sendMessage({
        'type': 'join',
        'data': {
          'user': 'flutter',
          'videoId': videoId.value,
        }
      });

      // Listen to incoming messages
      webSocketChannel?.stream.listen(
        (message) {
          try {
            LogService.log.i('📝 WebSocket message received: $message');

            // Parse JSON message
            // final data = jsonDecode(message);
            getComments();
          } catch (e) {
            LogService.log.e('❌ Error parsing WebSocket message: $e');
          }
        },
        onError: (error) {
          LogService.log.e('❌ WebSocket Error: $error');
        },
        onDone: () {
          LogService.log.w('⚠️ WebSocket connection closed');
        },
      );
    } catch (e) {
      LogService.log.e('❌ Failed to connect to WebSocket: $e');
      LogService.log.i('📱 App will continue without real-time features');
    }
  }

  void _sendMessage(Map<String, dynamic> message) {
    try {
      if (webSocketChannel != null) {
        final jsonMessage = jsonEncode(message);
        webSocketChannel!.sink.add(jsonMessage);
        LogService.log.i('📤 WebSocket message sent: $jsonMessage');
      }
    } catch (e) {
      LogService.log.e('❌ Error sending WebSocket message: $e');
    }
  }

  // void _handleWebSocketMessage(Map<String, dynamic> data) {
  //   try {
  //     final messageType = data['type'];

  //     switch (messageType) {
  //       case 'comment_update':
  //         LogService.log.i('💬 Comment update received');
  //         getComments(); // Refresh comments
  //         break;

  //       case 'new_comment':
  //         LogService.log.i('💬 New comment received');
  //         getComments(); // Refresh comments
  //         break;

  //       case 'user_joined':
  //         LogService.log.i('👤 User joined: ${data['data']}');
  //         break;

  //       case 'connection_confirmed':
  //         LogService.log.i('✅ WebSocket connection confirmed');
  //         break;

  //       default:
  //         LogService.log.i('📨 Unknown message type: $messageType');
  //     }
  //   } catch (e) {
  //     LogService.log.e('❌ Error handling WebSocket message: $e');
  //   }
  // }

  void disconnectSocket() {
    try {
      if (webSocketChannel != null) {
        LogService.log.i('🔌 Disconnecting from WebSocket...');
        webSocketChannel?.sink.close();
        webSocketChannel = null;
        LogService.log.i('✅ WebSocket disconnected successfully');
      } else {
        LogService.log.i('ℹ️ WebSocket was not connected');
      }
    } catch (e) {
      LogService.log.e('❌ Error disconnecting WebSocket: $e');
    }
  }

  // Helper method untuk safe argument access
  T? _getArgument<T>(String key, [T? defaultValue]) {
    try {
      return Get.arguments?[key] ?? defaultValue;
    } catch (e) {
      LogService.log.e('Error accessing argument $key: $e');
      return defaultValue;
    }
  }

  void initVideo() async {
    try {
      LogService.log.i('Initializing video with arguments: ${Get.arguments}');

      // Safe argument access dengan fallback values
      videoId.value = _getArgument<String>('videoId', '') ?? '';
      lastWatchTime.value =
          _getArgument<Duration>('lastWatchTime', lastWatchTime.value) ??
              lastWatchTime.value;

      final totalRating = _getArgument<num>('totalRating', 0.0);
      rattings.value = totalRating?.toDouble() ?? 0.0;

      videoUrl.value = _getArgument<String>(
              'videoUrl', 'https://cdn.jwplayer.com/manifests/pZxWPRg4.m3u8') ??
          'https://cdn.jwplayer.com/manifests/pZxWPRg4.m3u8';

      videoSourceUrl.value = _getArgument<String>('videoSourceUrl',
              'https://cdn.jwplayer.com/manifests/pZxWPRg4.m3u8') ??
          'https://cdn.jwplayer.com/manifests/pZxWPRg4.m3u8';

      LogService.log.i(
          'Video initialized - ID: ${videoId.value}, URL: ${videoUrl.value}');
    } catch (e) {
      LogService.log.e('Error during video initialization: $e');
      // Set default values on error
      videoUrl.value = 'https://cdn.jwplayer.com/manifests/pZxWPRg4.m3u8';
      videoSourceUrl.value = 'https://cdn.jwplayer.com/manifests/pZxWPRg4.m3u8';
      videoId.value = '';
      rattings.value = 0.0;
    }
    if (videoUrl.value.contains('youtube.com')) {
      LogService.log.i(
        'Video URL is a YouTube link: ${videoUrl.value}',
      );

      // Check if WebView is already in use
      if (_webViewInUse && _currentWebViewController != null) {
        LogService.log
            .w('⚠️ WebView already in use, disposing previous instance');
        try {
          _currentWebViewController!.loadRequest(Uri.parse('about:blank'));
          await Future.delayed(const Duration(milliseconds: 200));
        } catch (e) {
          LogService.log.e('Error disposing previous WebView: $e');
        }
      }

      // Create new WebViewController
      webViewController = WebViewController()
        ..setJavaScriptMode(JavaScriptMode.unrestricted)
        ..setBackgroundColor(Colors.transparent)
        // ..setNavigationDelegate(
        //   NavigationDelegate(
        //     onPageFinished: (url) async {
        //       if (url != videoUrl.value) {
        //         // Get.back();
        //       }
        //     },
        //   ),
        // )
        ..loadRequest(Uri.parse(videoUrl.value));

      // Update static tracking variables
      _webViewInUse = true;
      _currentWebViewController = webViewController;

      isInitialized.value = true;
      update();
    } else {
      LogService.log.i(
        'Video URL is not a YouTube link: ${videoUrl.value}',
      );
      videoPlayerController = VideoPlayerController.network(videoUrl.value)
        ..initialize().then((_) {
          // Setelah inisialisasi selesai, update status dan mulai memutar video
          isInitialized.value = true;
          chewieController = ChewieController(
            videoPlayerController: videoPlayerController,
            autoPlay: true,
            looping: true,
            startAt: lastWatchTime.value,
            materialProgressColors: ChewieProgressColors(
              playedColor: Colors.red,
              handleColor: Colors.redAccent,
            ),
          );
          update();
          videoPlayerController.play();
          isPlaying.value = true;

          startTimerPeriodic(videoId.value);

          LogService.log.i(
              'Duration Val: ${chewieController.videoPlayerController.value.position}');
        });
    }
  }

  void tambahRatting() async {
    var res = await VodPlaybackService()
        .createRattings(vodId: videoId.value, ratting: rattings.value);
    if (res.isSuccess) {
      LogService.log.i('Ratting added successfully');
    }
  }

  void startTimerPeriodic(String vodId) {
    timer = Timer.periodic(
      const Duration(seconds: 11),
      (_) async {
        final play = chewieController.videoPlayerController.value;

        final currentSec = play.position.inSeconds;
        // final totalDuration = play.duration.inSeconds;

        // final isTen = currentSec % 10 == 0;
        // final isNearEnd = totalDuration - currentSec <= 5;

        if (play.isPlaying) {
          await vodService.updatePlayback(vodId: vodId, duration: currentSec);
        } else {
          LogService.log.i('Duration Paused');
        }
      },
    );
  }

  void stopTimerPeriodic() {
    timer?.cancel();
    timer = null;
    // Handle app lifecycle changes
  }

  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    super.didChangeAppLifecycleState(state);

    switch (state) {
      case AppLifecycleState.paused:
      case AppLifecycleState.inactive:
        LogService.log.i('🔄 App paused/inactive - pausing video');
        // pauseVideo();
        break;
      case AppLifecycleState.detached:
        LogService.log.i('🔄 App detached - stopping video');
        // stopVideo();
        break;
      case AppLifecycleState.resumed:
        LogService.log.i('🔄 App resumed');
        break;
      case AppLifecycleState.hidden:
        LogService.log.i('🔄 App hidden - pausing video');
        // pauseVideo();
        break;
    }
  }

  // Method untuk menghentikan video secara eksplisit
  void stopVideo() async {
    try {
      LogService.log.i('🛑 Stopping video playback');

      if (videoUrl.value.contains('youtube.com')) {
        // Stop YouTube video with safer approach
        LogService.log.i('🌐 Stopping YouTube video');

        try {
          // Use simple DOM manipulation that's less likely to trigger CSP
          webViewController.runJavaScript('''
            (function() {
              try {
                var videos = document.getElementsByTagName('video');
                for (var i = 0; i < videos.length; i++) {
                  videos[i].pause();
                  videos[i].muted = true;
                  videos[i].volume = 0;
                }
              } catch(e) {
                // Silently handle errors to avoid CSP issues
              }
            })();
          ''');

          // Wait a moment then navigate away
          await Future.delayed(const Duration(milliseconds: 100));

          // Navigate to blank page to stop all activity
          webViewController.loadRequest(Uri.parse('about:blank'));
        } catch (e) {
          LogService.log
              .w('JavaScript execution failed, using navigation only: $e');
          // Fallback: just navigate to blank page
          webViewController.loadRequest(Uri.parse('about:blank'));
        }
      } else if (isInitialized.value &&
          videoPlayerController.value.isInitialized) {
        // Stop regular video player
        LogService.log.i('📹 Stopping regular video player');
        videoPlayerController.pause();
        videoPlayerController.seekTo(Duration.zero);
        isPlaying.value = false;
      }

      LogService.log.i('✅ Video stopped successfully');
    } catch (e) {
      LogService.log.e('❌ Error stopping video: $e');
    }
  }

  void getComments() async {
    comments.value = [];
    var res = await BroadcastService().getComment(
      id: videoId.value,
    );
    if (res.isSuccess) {
      comments.value = res.resultValue ?? [];
      LogService.log.i('Comments fetched successfully: ${comments.length}');
    } else {
      LogService.log.e('Failed to fetch comments: ${res.errorMessage}');
    }
  }

  void postComment({
    String? parentId,
    required String comment,
  }) async {
    var res = await BroadcastService().postComment(
      id: videoId.value,
      parentId: parentId,
      comment: comment,
    );
    if (res.isSuccess) {
      LogService.log.i('Comment posted successfully');

      // Notify other users via WebSocket
      // _sendMessage({
      //   'type': 'new_comment',
      //   'data': {
      //     'videoId': videoId.value,
      //     'comment': comment,
      //     'parentId': parentId,
      //     'timestamp': DateTime.now().toIso8601String(),
      //   }
      // });

      getComments();
      comments.refresh();
    } else {
      LogService.log.i('Failed to post comment: ${res.errorMessage}');
    }
  }

  // Method untuk toggle reply state
  void toggleReplyState(String commentId) {
    final index = comments.indexWhere((c) => c.id == commentId);
    if (index != -1) {
      comments[index].isOpenReply = !comments[index].isOpenReply;
      update(); // Trigger GetBuilder update
      LogService.log.i(
          'Toggled reply state for comment $commentId: ${comments[index].isOpenReply}');
    }
  }

  // Method untuk set reply state
  void setReplyState(String commentId, bool isOpen) {
    final index = comments.indexWhere((c) => c.id == commentId);
    if (index != -1) {
      comments[index].isOpenReply = isOpen;
      update(); // Trigger GetBuilder update
      LogService.log.i('Set reply state for comment $commentId: $isOpen');
    }
  }
}
