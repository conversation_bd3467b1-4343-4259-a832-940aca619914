import 'dart:io';
import 'package:flutter/material.dart';
import 'package:mides_skadik/app/data/constant/constants.dart';
import 'package:mides_skadik/app/data/models/response/video_editor/video_project.dart';
import 'package:mides_skadik/app/data/services/video_editor/video_service.dart';
import 'package:mides_skadik/widgets/video_editor/crop_widget.dart';
import 'package:mides_skadik/widgets/video_editor/filter_widget.dart';
import 'package:mides_skadik/widgets/video_editor/multi_layer_widget.dart';
import 'package:mides_skadik/widgets/video_editor/professional_timeline.dart';
import 'package:mides_skadik/widgets/video_editor/professional_transport_controls.dart';
import 'package:mides_skadik/widgets/video_editor/resizable_widget.dart';
import 'package:mides_skadik/widgets/video_editor/text_overlay_widget.dart';
import 'package:mides_skadik/widgets/video_editor/transform_widget.dart';
import 'package:video_player/video_player.dart';
import 'package:image_picker/image_picker.dart';
import 'package:file_picker/file_picker.dart';

class VideoEditorView extends StatefulWidget {
  final String? videoPath;
  final VideoProject? project;

  const VideoEditorView({
    super.key,
    this.videoPath,
    this.project,
  });

  @override
  State<VideoEditorView> createState() => _EditorScreenState();
}

class _EditorScreenState extends State<VideoEditorView> {
  late VideoPlayerController _videoController;
  late VideoProject _currentProject;
  final VideoService _videoService = VideoService.instance;
  final ImagePicker _imagePicker = ImagePicker();

  bool _isPlaying = false;
  bool _isLoading = true;
  bool _isExporting = false;
  double _currentPosition = 0;
  double _videoDuration = 0;

  // Trim/Cut state
  double? _trimStart;
  double? _trimEnd;
  bool _isTrimMode = false;
  bool _isCutMode = false;

  // Selected item for resizing
  String? _selectedItemId;

  @override
  void initState() {
    super.initState();
    _currentVideoPath = widget.videoPath;
    if (widget.videoPath != null) {
      _initializeVideo();
    } else {
      _initializeEmptyProject();
    }
    _initializeProject();
  }

  Future<void> _initializeVideo() async {
    if (widget.videoPath == null) return;

    try {
      _videoController = VideoPlayerController.file(File(widget.videoPath!));
      await _videoController.initialize();

      setState(() {
        _videoDuration =
            _videoController.value.duration.inMilliseconds / 1000.0;
        _isLoading = false;
      });

      _videoController.addListener(_videoListener);
    } catch (e) {
      print('Error initializing video: $e');
      setState(() {
        _isLoading = false;
      });
    }
  }

  void _initializeEmptyProject() {
    setState(() {
      _videoDuration = 60.0; // Default 60 seconds for empty project
      _isLoading = false;
    });

    // Create a dummy video controller for empty project
    // This will be replaced when first video is added
    _createDummyVideoController();
  }

  void _createDummyVideoController() {
    // For empty project, we'll initialize a minimal controller state
    // The UI will handle the null video case by showing empty canvas
    try {
      // Initialize with a dummy controller that won't be used
      // This prevents null reference errors in video control methods
      _isPlaying = false;
      _currentPosition = 0.0;
    } catch (e) {
      print('Error creating dummy controller: $e');
    }
  }

  Future<void> _initializeFirstVideo(String videoPath) async {
    try {
      // Dispose any existing controller if present
      if (widget.videoPath != null) {
        _videoController.removeListener(_videoListener);
        await _videoController.dispose();
      }

      // Initialize new video controller
      _videoController = VideoPlayerController.file(File(videoPath));
      await _videoController.initialize();

      // Update current video path
      _currentVideoPath = videoPath;

      // Add listener
      _videoController.addListener(_videoListener);

      if (mounted) {
        setState(() {});
      }
    } catch (e) {
      print('Error initializing first video: $e');
    }
  }

  void _initializeProject() {
    if (widget.project != null) {
      _currentProject = widget.project!;
    } else if (widget.videoPath != null) {
      // Create main video as MediaItem
      final mainVideoItem = MediaItem(
        id: 'main_video_${DateTime.now().millisecondsSinceEpoch}',
        type: MediaType.video,
        filePath: widget.videoPath,
        startTime: 0.0,
        endTime: _videoDuration,
        layer: 0, // Main video at layer 0
        // No x, y, width, height for main video - it fills the entire preview
      );

      // Initialize project with main video as first MediaItem
      _currentProject = VideoProject(
        id: DateTime.now().millisecondsSinceEpoch.toString(),
        name: 'New Project',
        mediaItems: [mainVideoItem],
        duration: _videoDuration,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      );
    } else {
      // Initialize empty project
      _currentProject = VideoProject(
        id: DateTime.now().millisecondsSinceEpoch.toString(),
        name: 'Empty Project',
        mediaItems: [],
        duration: _videoDuration,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      );
    }
  }

  void _videoListener() {
    if (mounted) {
      setState(() {
        _currentPosition =
            _videoController.value.position.inMilliseconds / 1000.0;
      });

      // Check if we need to switch to a different video based on timeline position
      _updateVideoForCurrentPosition();
    }
  }

  void _updateVideoForCurrentPosition() {
    // Find the video that should be playing at current position
    final videoItems = _currentProject.mediaItems
        .where((item) => item.type == MediaType.video)
        .where((item) =>
            _currentPosition >= item.startTime &&
            _currentPosition <= item.endTime)
        .toList();

    if (videoItems.isNotEmpty) {
      // Sort by layer to get the topmost video
      videoItems.sort((a, b) => b.layer.compareTo(a.layer));
      final activeVideo = videoItems.first;

      // Switch video controller if needed
      if (activeVideo.filePath != null &&
          activeVideo.filePath != _currentVideoPath) {
        _switchVideoController(activeVideo.filePath!, activeVideo);
      }
    }
  }

  String? _currentVideoPath;

  Future<void> _switchVideoController(
      String videoPath, MediaItem videoItem) async {
    if (_currentVideoPath == videoPath) return;

    try {
      // Dispose current controller
      _videoController.removeListener(_videoListener);
      await _videoController.dispose();

      // Create new controller
      _videoController = VideoPlayerController.file(File(videoPath));
      await _videoController.initialize();

      // Calculate relative position within the video
      final relativePosition = _currentPosition - videoItem.startTime;
      final videoDuration =
          _videoController.value.duration.inMilliseconds / 1000.0;

      if (relativePosition >= 0 && relativePosition <= videoDuration) {
        await _videoController
            .seekTo(Duration(milliseconds: (relativePosition * 1000).round()));
      }

      _videoController.addListener(_videoListener);
      _currentVideoPath = videoPath;

      if (mounted) {
        setState(() {});
      }
    } catch (e) {
      print('Error switching video controller: $e');
    }
  }

  @override
  void dispose() {
    if (_currentVideoPath != null) {
      _videoController.removeListener(_videoListener);
      _videoController.dispose();
    }
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Focus(
      autofocus: true,
      onKeyEvent: (node, event) {
        if (event.logicalKey.keyLabel == 'Delete' && _selectedItemId != null) {
          final selectedItem = _getSelectedMediaItem();
          if (selectedItem != null) {
            _deleteMediaItem(selectedItem);
            setState(() {
              _selectedItemId = null;
            });
          }
          return KeyEventResult.handled;
        }
        return KeyEventResult.ignored;
      },
      child: Scaffold(
        backgroundColor: Color(AppConstants.backgroundColor),
        appBar: AppBar(
          title: Text(
            _currentProject.name,
            style: const TextStyle(color: Colors.white),
          ),
          backgroundColor: Color(AppConstants.surfaceColor),
          iconTheme: const IconThemeData(color: Colors.white),
          actions: [
            if (_isExporting)
              const Padding(
                padding: EdgeInsets.all(16.0),
                child: SizedBox(
                  width: 20,
                  height: 20,
                  child: CircularProgressIndicator(strokeWidth: 2),
                ),
              )
            else
              IconButton(
                onPressed: _exportVideo,
                icon: const Icon(Icons.download),
                tooltip: 'Export Video',
              ),
            IconButton(
              onPressed: _saveProject,
              icon: const Icon(Icons.save),
              tooltip: 'Save Project',
            ),
          ],
        ),
        body: _isLoading
            ? const Center(
                child: CircularProgressIndicator(),
              )
            : Column(
                children: [
                  // Video preview
                  Expanded(
                    flex: 3,
                    child: Container(
                      color: Colors.black,
                      child: Center(
                        child: AspectRatio(
                          aspectRatio: _currentVideoPath != null &&
                                  _videoController.value.isInitialized
                              ? _videoController.value.aspectRatio
                              : 16 /
                                  9, // Default aspect ratio for empty project
                          child: GestureDetector(
                            onTap: () {
                              // Deselect item when tapping on empty area
                              setState(() {
                                _selectedItemId = null;
                              });
                            },
                            child: Stack(
                              children: [
                                // Main video player or empty canvas
                                if (_currentVideoPath != null &&
                                    _videoController.value.isInitialized)
                                  VideoPlayer(_videoController)
                                else
                                  _buildEmptyCanvas(),
                                // Overlay media items (all items including video overlays)
                                ..._buildOverlayItems(),
                                // Trim indicators
                                if (_isTrimMode) _buildTrimIndicators(),
                                // Cut indicators
                                if (_isCutMode) _buildCutIndicators(),
                              ],
                            ),
                          ),
                        ),
                      ),
                    ),
                  ),

                  // Professional Transport Controls
                  ProfessionalTransportControls(
                    isPlaying: _isPlaying,
                    currentPosition: _currentPosition,
                    duration: _videoDuration,
                    onPlayPause: _togglePlayPause,
                    onStop: _stopVideo,
                    onSeekBackward: _seekBackward,
                    onSeekForward: _seekForward,
                    onSkipToStart: _skipToStart,
                    onSkipToEnd: _skipToEnd,
                    isTrimMode: _isTrimMode,
                    isCutMode: _isCutMode,
                    onToggleTrimMode: _toggleTrimMode,
                    onToggleCutMode: _toggleCutMode,
                    onApplyTrimCut: _applyTrimCut,
                    onResetTrimCut: _resetTrimCut,
                    currentVideoPath: _getActiveVideoPath(),
                  ),

                  // Professional Timeline
                  ProfessionalTimeline(
                    duration: _videoDuration,
                    currentPosition: _currentPosition,
                    mediaItems: _currentProject.mediaItems,
                    onPositionChanged: _seekToPosition,
                    onMediaItemTap: _editMediaItem,
                    onMediaItemDelete: _deleteMediaItem,
                    onMediaItemResize: _resizeMediaItem,
                    onMediaItemMove: _moveMediaItem,
                    isTrimMode: _isTrimMode,
                    isCutMode: _isCutMode,
                    trimStart: _trimStart,
                    trimEnd: _trimEnd,
                    onTrimStartChanged: _updateTrimStart,
                    onTrimEndChanged: _updateTrimEnd,
                  ),

                  // Media Tools
                  Container(
                    height: AppConstants.toolbarHeight,
                    color: Color(AppConstants.surfaceColor),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                      children: [
                        _buildToolButton(
                          icon: Icons.text_fields,
                          label: 'Text',
                          onPressed: _addText,
                        ),
                        _buildToolButton(
                          icon: Icons.image,
                          label: 'Image',
                          onPressed: _addImage,
                        ),
                        _buildToolButton(
                          icon: Icons.audiotrack,
                          label: 'Audio',
                          onPressed: _addAudio,
                        ),
                        _buildToolButton(
                          icon: Icons.videocam,
                          label: 'Video',
                          onPressed: _addVideo,
                        ),
                      ],
                    ),
                  ),

                  // Effects & Transform Tools
                  Container(
                    height: AppConstants.toolbarHeight,
                    color: Color(AppConstants.backgroundColor),
                    child: SingleChildScrollView(
                      scrollDirection: Axis.horizontal,
                      child: Row(
                        children: [
                          _buildToolButton(
                            icon: Icons.filter,
                            label: 'Filter',
                            onPressed: _showFilterDialog,
                          ),
                          _buildToolButton(
                            icon: Icons.transform,
                            label: 'Transform',
                            onPressed: _showTransformDialog,
                          ),
                          _buildToolButton(
                            icon: Icons.layers,
                            label: 'Layers',
                            onPressed: _showLayerDialog,
                          ),
                          _buildToolButton(
                            icon: Icons.crop,
                            label: 'Crop',
                            onPressed: _showCropDialog,
                          ),
                          _buildToolButton(
                            icon: Icons.copy,
                            label: 'Duplicate',
                            onPressed: _duplicateSelectedItem,
                          ),
                        ],
                      ),
                    ),
                  ),
                ],
              ),
      ),
    );
  }

  Widget _buildEmptyCanvas() {
    return Container(
      width: double.infinity,
      height: double.infinity,
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            Colors.grey[900]!,
            Colors.grey[800]!,
          ],
        ),
        border: Border.all(
          color: Colors.white.withValues(alpha: 0.2),
          width: 2,
          style: BorderStyle.solid,
        ),
      ),
      child: Stack(
        children: [
          // Grid pattern
          CustomPaint(
            painter: GridPainter(),
            size: Size.infinite,
          ),
          // Center content with proper constraints
          Center(
            child: SingleChildScrollView(
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Icon(
                      Icons.video_library_outlined,
                      size: 48,
                      color: Colors.white.withValues(alpha: 0.3),
                    ),
                    const SizedBox(height: 12),
                    Text(
                      'Empty Canvas',
                      style: TextStyle(
                        color: Colors.white.withValues(alpha: 0.7),
                        fontSize: 20,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 6),
                    Text(
                      'Add videos, images, text, and audio to get started',
                      style: TextStyle(
                        color: Colors.white.withValues(alpha: 0.5),
                        fontSize: 12,
                      ),
                      textAlign: TextAlign.center,
                      maxLines: 2,
                      overflow: TextOverflow.ellipsis,
                    ),
                    const SizedBox(height: 16),
                    // Responsive button layout
                    LayoutBuilder(
                      builder: (context, constraints) {
                        if (constraints.maxWidth > 300) {
                          // Wide layout - horizontal buttons
                          return Row(
                            mainAxisAlignment: MainAxisAlignment.center,
                            mainAxisSize: MainAxisSize.min,
                            children: [
                              _buildQuickAddButton(
                                icon: Icons.videocam,
                                label: 'Video',
                                onTap: _addVideo,
                                compact: true,
                              ),
                              const SizedBox(width: 12),
                              _buildQuickAddButton(
                                icon: Icons.image,
                                label: 'Image',
                                onTap: _addImage,
                                compact: true,
                              ),
                              const SizedBox(width: 12),
                              _buildQuickAddButton(
                                icon: Icons.text_fields,
                                label: 'Text',
                                onTap: _addText,
                                compact: true,
                              ),
                            ],
                          );
                        } else {
                          // Narrow layout - vertical buttons
                          return Column(
                            mainAxisSize: MainAxisSize.min,
                            children: [
                              _buildQuickAddButton(
                                icon: Icons.videocam,
                                label: 'Add Video',
                                onTap: _addVideo,
                                compact: false,
                              ),
                              const SizedBox(height: 8),
                              _buildQuickAddButton(
                                icon: Icons.image,
                                label: 'Add Image',
                                onTap: _addImage,
                                compact: false,
                              ),
                              const SizedBox(height: 8),
                              _buildQuickAddButton(
                                icon: Icons.text_fields,
                                label: 'Add Text',
                                onTap: _addText,
                                compact: false,
                              ),
                            ],
                          );
                        }
                      },
                    ),
                  ],
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildQuickAddButton({
    required IconData icon,
    required String label,
    required VoidCallback onTap,
    bool compact = false,
  }) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        padding: compact
            ? const EdgeInsets.all(8)
            : const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
        constraints: compact
            ? const BoxConstraints(minWidth: 60, minHeight: 60)
            : const BoxConstraints(minWidth: 100, minHeight: 50),
        decoration: BoxDecoration(
          color: Color(AppConstants.accentColor).withValues(alpha: 0.2),
          borderRadius: BorderRadius.circular(8),
          border: Border.all(
            color: Color(AppConstants.accentColor).withValues(alpha: 0.5),
          ),
        ),
        child: compact
            ? Column(
                mainAxisSize: MainAxisSize.min,
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(
                    icon,
                    color: Color(AppConstants.accentColor),
                    size: 20,
                  ),
                  const SizedBox(height: 2),
                  Text(
                    label,
                    style: TextStyle(
                      color: Color(AppConstants.accentColor),
                      fontSize: 10,
                      fontWeight: FontWeight.bold,
                    ),
                    textAlign: TextAlign.center,
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                  ),
                ],
              )
            : Row(
                mainAxisSize: MainAxisSize.min,
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(
                    icon,
                    color: Color(AppConstants.accentColor),
                    size: 20,
                  ),
                  const SizedBox(width: 8),
                  Flexible(
                    child: Text(
                      label,
                      style: TextStyle(
                        color: Color(AppConstants.accentColor),
                        fontSize: 12,
                        fontWeight: FontWeight.bold,
                      ),
                      textAlign: TextAlign.center,
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                    ),
                  ),
                ],
              ),
      ),
    );
  }

  Widget _buildToolButton({
    required IconData icon,
    required String label,
    required VoidCallback onPressed,
  }) {
    return Column(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        IconButton(
          onPressed: onPressed,
          icon: Icon(icon, color: Colors.white),
        ),
        Text(
          label,
          style: const TextStyle(
            color: Colors.white,
            fontSize: 10,
          ),
        ),
      ],
    );
  }

  List<Widget> _buildOverlayItems() {
    final overlayItems = <Widget>[];

    // Sort items by layer (lower layer numbers appear first/behind)
    final sortedItems = List<MediaItem>.from(_currentProject.mediaItems)
      ..sort((a, b) => a.layer.compareTo(b.layer));

    for (final item in sortedItems) {
      if (item.startTime <= _currentPosition &&
          _currentPosition <= item.endTime &&
          item.opacity > 0) {
        Widget? itemWidget;

        switch (item.type) {
          case MediaType.text:
            if (item.text != null) {
              itemWidget = Text(
                item.text!,
                style: TextStyle(
                  color: Color(item.textColor ?? 0xFFFFFFFF),
                  fontSize: item.fontSize ?? AppConstants.defaultTextSize,
                  fontWeight: FontWeight.bold,
                  shadows: [
                    Shadow(
                      offset: const Offset(1, 1),
                      blurRadius: 2,
                      color: Colors.black.withValues(alpha: 0.7),
                    ),
                  ],
                ),
              );
            }
            break;
          case MediaType.image:
            if (item.filePath != null) {
              itemWidget = SizedBox(
                width: item.width ?? 100,
                height: item.height ?? 100,
                child: Image.file(
                  File(item.filePath!),
                  fit: BoxFit.cover,
                ),
              );
            }
            break;
          case MediaType.video:
            // Skip all video items - they are handled by the main video player
            // Videos will be switched automatically based on timeline position
            break;
          default:
            break;
        }

        if (itemWidget != null) {
          // Apply transform if available
          if (item.transformSettings != null) {
            itemWidget = _applyTransform(itemWidget, item.transformSettings!);
          }

          // Apply filter if available
          if (item.filterSettings != null) {
            itemWidget = _applyFilter(itemWidget, item.filterSettings!);
          }

          // Apply opacity
          if (item.opacity < 1.0) {
            itemWidget = Opacity(
              opacity: item.opacity,
              child: itemWidget,
            );
          }

          // Wrap with ResizableWidget for interactive resize
          overlayItems.add(
            ResizableWidget(
              mediaItem: item,
              isSelected: _selectedItemId == item.id,
              onTap: () {
                setState(() {
                  _selectedItemId = _selectedItemId == item.id ? null : item.id;
                });
              },
              onChanged: (updatedItem) {
                _updateMediaItemFromResize(updatedItem);
              },
              child: itemWidget,
            ),
          );
        }
      }
    }

    return overlayItems;
  }

  Widget _buildTrimIndicators() {
    return Stack(
      children: [
        // Start trim indicator
        if (_trimStart != null)
          Positioned(
            left: (_trimStart! / _videoDuration) *
                MediaQuery.of(context).size.width,
            top: 0,
            bottom: 0,
            child: Container(
              width: 3,
              color: Color(AppConstants.accentColor),
              child: const Align(
                alignment: Alignment.topCenter,
                child: Icon(
                  Icons.arrow_drop_down,
                  color: Colors.white,
                  size: 20,
                ),
              ),
            ),
          ),
        // End trim indicator
        if (_trimEnd != null)
          Positioned(
            left: (_trimEnd! / _videoDuration) *
                MediaQuery.of(context).size.width,
            top: 0,
            bottom: 0,
            child: Container(
              width: 3,
              color: Color(AppConstants.accentColor),
              child: const Align(
                alignment: Alignment.topCenter,
                child: Icon(
                  Icons.arrow_drop_down,
                  color: Colors.white,
                  size: 20,
                ),
              ),
            ),
          ),
      ],
    );
  }

  Widget _buildCutIndicators() {
    return Stack(
      children: [
        // Cut indicators (similar to trim but different color)
        if (_trimStart != null)
          Positioned(
            left: (_trimStart! / _videoDuration) *
                MediaQuery.of(context).size.width,
            top: 0,
            bottom: 0,
            child: Container(
              width: 3,
              color: Colors.red,
              child: const Align(
                alignment: Alignment.topCenter,
                child: Icon(
                  Icons.content_cut,
                  color: Colors.white,
                  size: 16,
                ),
              ),
            ),
          ),
        if (_trimEnd != null)
          Positioned(
            left: (_trimEnd! / _videoDuration) *
                MediaQuery.of(context).size.width,
            top: 0,
            bottom: 0,
            child: Container(
              width: 3,
              color: Colors.red,
              child: const Align(
                alignment: Alignment.topCenter,
                child: Icon(
                  Icons.content_cut,
                  color: Colors.white,
                  size: 16,
                ),
              ),
            ),
          ),
      ],
    );
  }

  // Video control methods
  void _togglePlayPause() {
    if (_currentVideoPath == null || !_videoController.value.isInitialized) {
      _showErrorSnackBar('No video loaded. Add a video first.');
      return;
    }

    setState(() {
      if (_isPlaying) {
        _videoController.pause();
      } else {
        _videoController.play();
      }
      _isPlaying = !_isPlaying;
    });
  }

  void _seekBackward() {
    final newPosition = (_currentPosition - 10).clamp(0.0, _videoDuration);
    _seekToPosition(newPosition);
  }

  void _seekForward() {
    final newPosition = (_currentPosition + 10).clamp(0.0, _videoDuration);
    _seekToPosition(newPosition);
  }

  void _seekToPosition(double position) {
    setState(() {
      _currentPosition = position;
    });

    // Adjust position if in cut mode
    double adjustedPosition = position;

    if (_isCutMode && _trimStart != null && _trimEnd != null) {
      adjustedPosition = _getAdjustedPositionForCutMode(position);
    }

    // Find the video that should be playing at this position
    final videoItems = _currentProject.mediaItems
        .where((item) => item.type == MediaType.video)
        .where((item) =>
            adjustedPosition >= item.startTime &&
            adjustedPosition <= item.endTime)
        .toList();

    if (videoItems.isNotEmpty) {
      // Sort by layer to get the topmost video
      videoItems.sort((a, b) => b.layer.compareTo(a.layer));
      final activeVideo = videoItems.first;

      if (activeVideo.filePath != null) {
        // Switch video controller if needed
        if (activeVideo.filePath != _currentVideoPath) {
          _switchVideoController(activeVideo.filePath!, activeVideo);
        } else {
          // Same video, just seek to relative position
          final relativePosition = adjustedPosition - activeVideo.startTime;
          final duration =
              Duration(milliseconds: (relativePosition * 1000).round());
          _videoController.seekTo(duration);
        }
      }
    }
  }

  // Helper method to adjust position for cut mode
  double _getAdjustedPositionForCutMode(double timelinePosition) {
    if (!_isCutMode || _trimStart == null || _trimEnd == null) {
      return timelinePosition;
    }

    final cutStart = _trimStart!;
    final cutEnd = _trimEnd!;
    final cutDuration = cutEnd - cutStart;

    if (timelinePosition < cutStart) {
      // Position is in the first segment - no adjustment needed
      return timelinePosition;
    } else {
      // Position is in the second segment - add back the cut duration
      return timelinePosition + cutDuration;
    }
  }

  void _stopVideo() {
    if (_currentVideoPath != null && _videoController.value.isInitialized) {
      _videoController.pause();
      _seekToPosition(0.0);
      setState(() {
        _isPlaying = false;
      });
    }
  }

  void _skipToStart() {
    _seekToPosition(0.0);
  }

  void _skipToEnd() {
    _seekToPosition(_videoDuration);
  }

  // Trim and Cut methods
  void _toggleTrimMode() {
    // Check if we have any video to trim
    String? videoPath = _getActiveVideoPath();
    if (videoPath == null && !_isTrimMode) {
      _showErrorSnackBar('Please load a video first before using trim mode');
      return;
    }

    setState(() {
      _isTrimMode = !_isTrimMode;
      _isCutMode = false;
      if (_isTrimMode) {
        _trimStart = _currentPosition;
        _trimEnd = (_currentPosition + 5).clamp(0.0, _videoDuration);
        _showSuccessSnackBar(
            'Trim mode activated. Drag the blue markers to set trim points.');
      } else {
        _trimStart = null;
        _trimEnd = null;
        _showSuccessSnackBar('Trim mode deactivated.');
      }
    });
  }

  void _toggleCutMode() {
    // Simple cut: immediately cut at current position
    _performSimpleCut();
  }

  void _performSimpleCut() async {
    // Check if we have any video to cut
    String? videoPath = _getActiveVideoPath();
    if (videoPath == null) {
      _showErrorSnackBar('Please load a video first before cutting');
      return;
    }

    // Show confirmation dialog
    bool? shouldCut = await _showCutConfirmationDialog();
    if (shouldCut != true) return;

    setState(() {
      _isExporting = true;
    });

    try {
      // Cut the video at current position (split into two parts)
      String? outputPath = await _videoService.cutVideoAtPosition(
        inputPath: videoPath,
        cutPosition: _currentPosition,
      );

      if (outputPath != null) {
        _showSuccessSnackBar('Video cut successfully! Split into two parts.');

        // Optionally reload the first part
        // Navigator.pushReplacement(context, MaterialPageRoute(
        //   builder: (context) => EditorScreen(videoPath: outputPath),
        // ));
      } else {
        _showErrorSnackBar('Failed to cut video');
      }
    } catch (e) {
      _showErrorSnackBar('Error cutting video: $e');
    } finally {
      setState(() {
        _isExporting = false;
      });
    }
  }

  Future<bool?> _showCutConfirmationDialog() {
    return showDialog<bool>(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          backgroundColor: Color(AppConstants.surfaceColor),
          title: Row(
            children: [
              Icon(Icons.call_split, color: Colors.red, size: 24),
              SizedBox(width: 8),
              Text(
                'Cut Video',
                style: TextStyle(color: Colors.white),
              ),
            ],
          ),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                'Cut video at current position?',
                style: TextStyle(color: Colors.white, fontSize: 16),
              ),
              SizedBox(height: 12),
              Container(
                padding: EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: Colors.blue.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(color: Colors.blue, width: 1),
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Current Position: ${_formatTime(_currentPosition)}',
                      style: TextStyle(
                          color: Colors.blue, fontWeight: FontWeight.bold),
                    ),
                    SizedBox(height: 4),
                    Text(
                      'This will split your video into two separate files.',
                      style: TextStyle(color: Colors.white70, fontSize: 12),
                    ),
                  ],
                ),
              ),
            ],
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(false),
              child: Text('Cancel', style: TextStyle(color: Colors.grey)),
            ),
            ElevatedButton(
              onPressed: () => Navigator.of(context).pop(true),
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.red,
                foregroundColor: Colors.white,
              ),
              child: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Icon(Icons.call_split, size: 16),
                  SizedBox(width: 4),
                  Text('Cut Here'),
                ],
              ),
            ),
          ],
        );
      },
    );
  }

  void _updateTrimStart(double newStart) {
    setState(() {
      _trimStart = newStart;
    });
  }

  void _updateTrimEnd(double newEnd) {
    setState(() {
      _trimEnd = newEnd;
    });
  }

  void _applyTrimCut() async {
    if (_trimStart == null || _trimEnd == null) {
      _showErrorSnackBar('Please set trim/cut points first');
      return;
    }

    // Get the video path from multiple sources
    String? videoPath = _getActiveVideoPath();

    if (videoPath == null) {
      _showErrorSnackBar('No video loaded to trim/cut');
      return;
    }

    setState(() {
      _isExporting = true;
    });

    try {
      String? outputPath;

      if (_isTrimMode) {
        outputPath = await _videoService.trimVideo(
          inputPath: videoPath,
          startTime: _trimStart!,
          endTime: _trimEnd!,
        );
        _showSuccessSnackBar('Video trimmed successfully!');
      } else if (_isCutMode) {
        outputPath = await _videoService.cutVideo(
          inputPath: videoPath,
          cutStart: _trimStart!,
          cutEnd: _trimEnd!,
        );
        _showSuccessSnackBar('Video cut successfully!');
      }

      if (outputPath != null) {
        // Reset trim/cut mode
        _resetTrimCut();

        // Optionally, you could load the new video
        // Navigator.pushReplacement(context, MaterialPageRoute(
        //   builder: (context) => EditorScreen(videoPath: outputPath),
        // ));
      } else {
        _showErrorSnackBar('Failed to process video');
      }
    } catch (e) {
      _showErrorSnackBar('Error processing video: $e');
    } finally {
      setState(() {
        _isExporting = false;
      });
    }
  }

  void _resetTrimCut() {
    setState(() {
      _isTrimMode = false;
      _isCutMode = false;
      _trimStart = null;
      _trimEnd = null;
    });
    _showSuccessSnackBar('Trim/Cut cancelled');
  }

  // Helper method to get active video path from multiple sources
  String? _getActiveVideoPath() {
    // Priority 1: Current video path (from video switching)
    if (_currentVideoPath != null) {
      print('DEBUG: Using current video path: $_currentVideoPath');
      return _currentVideoPath;
    }

    // Priority 2: Original widget video path
    if (widget.videoPath != null) {
      print('DEBUG: Using widget video path: ${widget.videoPath}');
      return widget.videoPath;
    }

    // Priority 3: Find video from current project media items
    final videoItems = _currentProject.mediaItems
        .where((item) => item.type == MediaType.video && item.filePath != null)
        .toList();

    print('DEBUG: Found ${videoItems.length} video items in project');

    if (videoItems.isNotEmpty) {
      // Find video that contains current position
      final activeVideo = videoItems.firstWhere(
        (item) =>
            _currentPosition >= item.startTime &&
            _currentPosition <= item.endTime,
        orElse: () => videoItems.first, // Fallback to first video
      );
      print('DEBUG: Using project video path: ${activeVideo.filePath}');
      return activeVideo.filePath;
    }

    print('DEBUG: No video path found');
    return null;
  }

  String _formatTime(double seconds) {
    final duration = Duration(seconds: seconds.round());
    final hours = duration.inHours;
    final minutes = duration.inMinutes.remainder(60);
    final secs = duration.inSeconds.remainder(60);

    if (hours > 0) {
      return '${hours.toString().padLeft(2, '0')}:'
          '${minutes.toString().padLeft(2, '0')}:'
          '${secs.toString().padLeft(2, '0')}';
    } else {
      return '${minutes.toString().padLeft(2, '0')}:'
          '${secs.toString().padLeft(2, '0')}';
    }
  }

  // Media editing methods
  void _addText() {
    showDialog(
      context: context,
      builder: (context) => TextOverlayWidget(
        onTextUpdated: (textItem) {
          // Ensure text gets the correct layer
          final updatedTextItem = textItem.copyWith(
            layer: _getNextLayerForType(MediaType.text),
          );

          setState(() {
            _currentProject = _currentProject.copyWith(
              mediaItems: [..._currentProject.mediaItems, updatedTextItem],
              updatedAt: DateTime.now(),
            );
          });
          Navigator.pop(context);
        },
        onCancel: () => Navigator.pop(context),
      ),
    );
  }

  Future<void> _addImage() async {
    try {
      final XFile? image =
          await _imagePicker.pickImage(source: ImageSource.gallery);
      if (image != null) {
        final fileName = image.path.split('/').last;

        final imageItem = MediaItem(
          id: DateTime.now().millisecondsSinceEpoch.toString(),
          type: MediaType.image,
          filePath: image.path,
          startTime: _currentPosition,
          endTime: (_currentPosition + 5).clamp(0.0, _videoDuration),
          x: 50,
          y: 50,
          width: 150,
          height: 150,
          layer: _getNextLayerForType(MediaType.image),
        );

        setState(() {
          _currentProject = _currentProject.copyWith(
            mediaItems: [..._currentProject.mediaItems, imageItem],
            updatedAt: DateTime.now(),
          );
        });

        _showSuccessSnackBar('Image "$fileName" added successfully');
      }
    } catch (e) {
      _showErrorSnackBar('Error adding image: $e');
    }
  }

  Future<void> _addAudio() async {
    try {
      final result = await FilePicker.platform.pickFiles(
        type: FileType.audio,
      );

      if (result != null && result.files.single.path != null) {
        final file = result.files.single;
        final fileName = file.name;

        // Validate file extension
        final extension = fileName.split('.').last.toLowerCase();
        if (!AppConstants.supportedAudioFormats.contains(extension)) {
          _showErrorSnackBar(
              'Unsupported audio format. Supported formats: ${AppConstants.supportedAudioFormats.join(', ')}');
          return;
        }

        final audioItem = MediaItem(
          id: DateTime.now().millisecondsSinceEpoch.toString(),
          type: MediaType.audio,
          filePath: file.path!,
          startTime: _currentPosition,
          endTime: _videoDuration,
          volume: 0.5,
          layer: _getNextLayerForType(MediaType.audio),
        );

        setState(() {
          _currentProject = _currentProject.copyWith(
            mediaItems: [..._currentProject.mediaItems, audioItem],
            updatedAt: DateTime.now(),
          );
        });

        _showSuccessSnackBar('Audio "$fileName" added successfully');
      }
    } catch (e) {
      _showErrorSnackBar('Error adding audio: $e');
    }
  }

  Future<void> _addVideo() async {
    try {
      final XFile? video =
          await _imagePicker.pickVideo(source: ImageSource.gallery);
      if (video != null) {
        // Get video duration
        final controller = VideoPlayerController.file(File(video.path));
        await controller.initialize();
        final videoDuration = controller.value.duration.inMilliseconds / 1000.0;
        controller.dispose();

        // Calculate start time - place after the last video
        double startTime = 0.0;
        final videoItems = _currentProject.mediaItems
            .where((item) => item.type == MediaType.video)
            .toList();

        if (videoItems.isNotEmpty) {
          // Find the latest end time of existing videos
          final latestEndTime = videoItems
              .map((item) => item.endTime)
              .reduce((a, b) => a > b ? a : b);
          startTime = latestEndTime;
        }

        final endTime = startTime + videoDuration;

        // Update project duration if needed
        final newProjectDuration =
            endTime > _videoDuration ? endTime : _videoDuration;

        final videoItem = MediaItem(
          id: DateTime.now().millisecondsSinceEpoch.toString(),
          type: MediaType.video,
          filePath: video.path,
          startTime: startTime,
          endTime: endTime,
          layer: _getNextLayerForType(MediaType.video),
          // No x, y, width, height for videos - they fill the entire preview
        );

        setState(() {
          _videoDuration = newProjectDuration;
          _currentProject = _currentProject.copyWith(
            mediaItems: [..._currentProject.mediaItems, videoItem],
            duration: newProjectDuration,
            updatedAt: DateTime.now(),
          );
        });

        // If this is the first video in an empty project, initialize video controller
        if (widget.videoPath == null && videoItems.isEmpty) {
          await _initializeFirstVideo(video.path);
        }

        // Seek to the start of the new video to show it in preview
        _seekToPosition(startTime);

        _showSuccessSnackBar('Video added successfully');
      }
    } catch (e) {
      _showErrorSnackBar('Error adding video: $e');
    }
  }

  void _editMediaItem(MediaItem item) {
    if (item.type == MediaType.text) {
      showDialog(
        context: context,
        builder: (context) => TextOverlayWidget(
          textItem: item,
          onTextUpdated: (updatedItem) {
            setState(() {
              final items = _currentProject.mediaItems.map((i) {
                return i.id == item.id ? updatedItem : i;
              }).toList();

              _currentProject = _currentProject.copyWith(
                mediaItems: items,
                updatedAt: DateTime.now(),
              );
            });
            Navigator.pop(context);
          },
          onCancel: () => Navigator.pop(context),
        ),
      );
    }
  }

  void _deleteMediaItem(MediaItem item) {
    setState(() {
      final items =
          _currentProject.mediaItems.where((i) => i.id != item.id).toList();
      _currentProject = _currentProject.copyWith(
        mediaItems: items,
        updatedAt: DateTime.now(),
      );
    });
  }

  Future<void> _exportVideo() async {
    if (_isExporting) return;

    // Check if project has any media
    if (_currentProject.mediaItems.isEmpty) {
      _showErrorSnackBar('Cannot export empty project. Add some media first.');
      return;
    }

    setState(() {
      _isExporting = true;
    });

    try {
      String? outputPath;

      // Apply trim or cut if active and we have a main video
      if (_isTrimMode &&
          _trimStart != null &&
          _trimEnd != null &&
          widget.videoPath != null) {
        outputPath = await _videoService.trimVideo(
          inputPath: widget.videoPath!,
          startTime: _trimStart!,
          endTime: _trimEnd!,
        );
      } else if (_isCutMode &&
          _trimStart != null &&
          _trimEnd != null &&
          widget.videoPath != null) {
        outputPath = await _videoService.cutVideo(
          inputPath: widget.videoPath!,
          cutStart: _trimStart!,
          cutEnd: _trimEnd!,
        );
      } else {
        // Export with all media items
        outputPath = await _videoService.exportProject(_currentProject);
      }

      if (outputPath != null) {
        _showSuccessSnackBar('Video exported successfully to: $outputPath');
      } else {
        _showErrorSnackBar('Failed to export video');
      }
    } catch (e) {
      _showErrorSnackBar('Error exporting video: $e');
    } finally {
      setState(() {
        _isExporting = false;
      });
    }
  }

  void _saveProject() {
    // TODO: Implement project saving to local storage
    _showSuccessSnackBar('Project saved successfully');
  }

  void _showErrorSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: Colors.red,
      ),
    );
  }

  void _showSuccessSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: Colors.green,
      ),
    );
  }

  // New methods for filter, transform, and multi-layer features
  void _showFilterDialog() {
    // Get currently selected media item or apply to main video
    MediaItem? selectedItem = _getSelectedMediaItem();

    showDialog(
      context: context,
      builder: (context) => FilterWidget(
        currentFilter: selectedItem?.filterSettings,
        onFilterChanged: (filterSettings) {
          if (selectedItem != null) {
            _updateMediaItemFilter(selectedItem, filterSettings);
          } else {
            _showErrorSnackBar('Please select a media item to apply filter');
          }
        },
        onCancel: () => Navigator.pop(context),
      ),
    );
  }

  void _showTransformDialog() {
    MediaItem? selectedItem = _getSelectedMediaItem();

    if (selectedItem == null) {
      _showErrorSnackBar('Please select a media item to transform');
      return;
    }

    showDialog(
      context: context,
      builder: (context) => TransformWidget(
        currentTransform: selectedItem.transformSettings,
        onTransformChanged: (transformSettings) {
          _updateMediaItemTransform(selectedItem, transformSettings);
        },
        onCancel: () => Navigator.pop(context),
      ),
    );
  }

  void _showLayerDialog() {
    showDialog(
      context: context,
      builder: (context) => MultiLayerWidget(
        mediaItems: _currentProject.mediaItems,
        onLayersChanged: (updatedItems) {
          setState(() {
            _currentProject = _currentProject.copyWith(
              mediaItems: updatedItems,
              updatedAt: DateTime.now(),
            );
          });
        },
        onItemSelected: (item) {
          // Could implement item selection highlighting here
        },
        onCancel: () => Navigator.pop(context),
      ),
    );
  }

  void _showCropDialog() {
    MediaItem? selectedItem = _getSelectedMediaItem();

    if (selectedItem == null) {
      _showErrorSnackBar('Please select a media item to crop');
      return;
    }

    // Only allow crop for visual items (image, video, text)
    if (selectedItem.type == MediaType.audio) {
      _showErrorSnackBar('Cannot crop audio items');
      return;
    }

    showDialog(
      context: context,
      builder: (context) => CropWidget(
        mediaItem: selectedItem,
        onCropChanged: (updatedItem) {
          setState(() {
            final items = _currentProject.mediaItems.map((i) {
              return i.id == selectedItem.id ? updatedItem : i;
            }).toList();

            _currentProject = _currentProject.copyWith(
              mediaItems: items,
              updatedAt: DateTime.now(),
            );
          });
          _showSuccessSnackBar('Item cropped successfully');
        },
        onCancel: () => Navigator.pop(context),
      ),
    );
  }

  void _duplicateSelectedItem() {
    MediaItem? selectedItem = _getSelectedMediaItem();

    if (selectedItem == null) {
      _showErrorSnackBar('Please select a media item to duplicate');
      return;
    }

    final duplicatedItem = selectedItem.copyWith(
      id: DateTime.now().millisecondsSinceEpoch.toString(),
      startTime: selectedItem.endTime,
      endTime: selectedItem.endTime +
          (selectedItem.endTime - selectedItem.startTime),
      layer: _getNextAvailableLayer(),
    );

    setState(() {
      _currentProject = _currentProject.copyWith(
        mediaItems: [..._currentProject.mediaItems, duplicatedItem],
        updatedAt: DateTime.now(),
      );
    });

    _showSuccessSnackBar('Media item duplicated');
  }

  MediaItem? _getSelectedMediaItem() {
    if (_selectedItemId != null) {
      return _currentProject.mediaItems
          .where((item) => item.id == _selectedItemId)
          .firstOrNull;
    }

    // Fallback: return the last added item or null
    if (_currentProject.mediaItems.isNotEmpty) {
      return _currentProject.mediaItems.last;
    }
    return null;
  }

  void _updateMediaItemFilter(MediaItem item, FilterSettings? filterSettings) {
    setState(() {
      final items = _currentProject.mediaItems.map((i) {
        return i.id == item.id ? i.copyWith(filterSettings: filterSettings) : i;
      }).toList();

      _currentProject = _currentProject.copyWith(
        mediaItems: items,
        updatedAt: DateTime.now(),
      );
    });

    _showSuccessSnackBar('Filter applied');
  }

  void _updateMediaItemTransform(
      MediaItem item, TransformSettings? transformSettings) {
    setState(() {
      final items = _currentProject.mediaItems.map((i) {
        return i.id == item.id
            ? i.copyWith(transformSettings: transformSettings)
            : i;
      }).toList();

      _currentProject = _currentProject.copyWith(
        mediaItems: items,
        updatedAt: DateTime.now(),
      );
    });

    _showSuccessSnackBar('Transform applied');
  }

  int _getNextAvailableLayer() {
    if (_currentProject.mediaItems.isEmpty) return 0;

    final maxLayer = _currentProject.mediaItems
        .map((item) => item.layer)
        .reduce((a, b) => a > b ? a : b);

    return maxLayer + 1;
  }

  int _getNextLayerForType(MediaType type) {
    // Get the highest layer for this media type
    final itemsOfType =
        _currentProject.mediaItems.where((item) => item.type == type).toList();

    if (itemsOfType.isEmpty) {
      // First item of this type, assign based on type priority
      switch (type) {
        case MediaType.video:
          return 0; // Video starts at layer 0
        case MediaType.audio:
          return 100; // Audio starts at layer 100 (separate from video layers)
        case MediaType.image:
          return 200; // Images start at layer 200
        case MediaType.text:
          return 300; // Text starts at layer 300
      }
    }

    // Find the highest layer for this type and add 1
    final maxLayerForType =
        itemsOfType.map((item) => item.layer).reduce((a, b) => a > b ? a : b);

    return maxLayerForType + 1;
  }

  // Timeline editing methods
  void _resizeMediaItem(
      MediaItem item, double newStartTime, double newEndTime) {
    setState(() {
      final items = _currentProject.mediaItems.map((i) {
        return i.id == item.id
            ? i.copyWith(startTime: newStartTime, endTime: newEndTime)
            : i;
      }).toList();

      _currentProject = _currentProject.copyWith(
        mediaItems: items,
        updatedAt: DateTime.now(),
      );
    });
  }

  void _moveMediaItem(MediaItem item, double newStartTime) {
    setState(() {
      final duration = item.endTime - item.startTime;
      final newEndTime = newStartTime + duration;

      final items = _currentProject.mediaItems.map((i) {
        return i.id == item.id
            ? i.copyWith(startTime: newStartTime, endTime: newEndTime)
            : i;
      }).toList();

      _currentProject = _currentProject.copyWith(
        mediaItems: items,
        updatedAt: DateTime.now(),
      );
    });
  }

  void _updateMediaItemFromResize(MediaItem updatedItem) {
    setState(() {
      final items = _currentProject.mediaItems.map((i) {
        return i.id == updatedItem.id ? updatedItem : i;
      }).toList();

      _currentProject = _currentProject.copyWith(
        mediaItems: items,
        updatedAt: DateTime.now(),
      );
    });
  }

  Widget _applyTransform(Widget widget, TransformSettings transform) {
    return Transform(
      alignment: Alignment.center,
      transform: Matrix4.identity()
        ..scale(transform.scaleX, transform.scaleY)
        ..rotateZ(
            transform.rotation * 3.14159 / 180) // Convert degrees to radians
        ..translate(transform.translateX, transform.translateY),
      child: transform.flipHorizontal || transform.flipVertical
          ? Transform.flip(
              flipX: transform.flipHorizontal,
              flipY: transform.flipVertical,
              child: widget,
            )
          : widget,
    );
  }

  Widget _applyFilter(Widget widget, FilterSettings filter) {
    // For now, apply basic color filters using ColorFiltered
    // In a real implementation, you'd use more sophisticated image processing

    ColorFilter? colorFilter;

    switch (filter.type) {
      case FilterType.blackWhite:
        colorFilter = const ColorFilter.matrix([
          0.2126,
          0.7152,
          0.0722,
          0,
          0,
          0.2126,
          0.7152,
          0.0722,
          0,
          0,
          0.2126,
          0.7152,
          0.0722,
          0,
          0,
          0,
          0,
          0,
          1,
          0,
        ]);
        break;
      case FilterType.sepia:
        colorFilter = const ColorFilter.matrix([
          0.393,
          0.769,
          0.189,
          0,
          0,
          0.349,
          0.686,
          0.168,
          0,
          0,
          0.272,
          0.534,
          0.131,
          0,
          0,
          0,
          0,
          0,
          1,
          0,
        ]);
        break;
      case FilterType.vintage:
        colorFilter = const ColorFilter.matrix([
          0.9,
          0.5,
          0.1,
          0,
          0,
          0.3,
          0.8,
          0.1,
          0,
          0,
          0.2,
          0.3,
          0.5,
          0,
          0,
          0,
          0,
          0,
          1,
          0,
        ]);
        break;
      case FilterType.warm:
        colorFilter = const ColorFilter.matrix([
          1.2,
          0,
          0,
          0,
          0,
          0,
          1.0,
          0,
          0,
          0,
          0,
          0,
          0.8,
          0,
          0,
          0,
          0,
          0,
          1,
          0,
        ]);
        break;
      case FilterType.cool:
        colorFilter = const ColorFilter.matrix([
          0.8,
          0,
          0,
          0,
          0,
          0,
          1.0,
          0,
          0,
          0,
          0,
          0,
          1.2,
          0,
          0,
          0,
          0,
          0,
          1,
          0,
        ]);
        break;
      default:
        // For other filters, apply intensity-based modifications
        final intensity = filter.intensity;
        if (filter.parameters.containsKey('brightness')) {
          final brightness = filter.parameters['brightness']! * intensity;
          colorFilter = ColorFilter.matrix([
            1,
            0,
            0,
            0,
            brightness * 255,
            0,
            1,
            0,
            0,
            brightness * 255,
            0,
            0,
            1,
            0,
            brightness * 255,
            0,
            0,
            0,
            1,
            0,
          ]);
        }
        break;
    }

    if (colorFilter != null) {
      widget = ColorFiltered(
        colorFilter: colorFilter,
        child: widget,
      );
    }

    // Apply blur if specified
    if (filter.type == FilterType.blur &&
        filter.parameters.containsKey('radius')) {
      final radius = filter.parameters['radius']! * filter.intensity;
      // Note: For real blur effect, you'd need to use ImageFilter.blur
      // This is a simplified version
      widget = Container(
        decoration: BoxDecoration(
          boxShadow: [
            BoxShadow(
              color: Colors.black.withValues(alpha: 0.1),
              blurRadius: radius,
              spreadRadius: radius / 2,
            ),
          ],
        ),
        child: widget,
      );
    }

    return widget;
  }
}

class GridPainter extends CustomPainter {
  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..color = Colors.white.withValues(alpha: 0.1)
      ..strokeWidth = 1;

    const gridSize = 20.0;

    // Draw vertical lines
    for (double x = 0; x <= size.width; x += gridSize) {
      canvas.drawLine(
        Offset(x, 0),
        Offset(x, size.height),
        paint,
      );
    }

    // Draw horizontal lines
    for (double y = 0; y <= size.height; y += gridSize) {
      canvas.drawLine(
        Offset(0, y),
        Offset(size.width, y),
        paint,
      );
    }
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => false;
}
