import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:mides_skadik/app/data/utils/utils.dart';
import 'package:mides_skadik/app/modules/course/clases_course/controllers/hanjar_controller.dart';
import 'package:mides_skadik/app/modules/course/clases_course/views/detail_assignment_view.dart';
import 'package:mides_skadik/themes/colors.dart';
import 'package:mides_skadik/widgets/components/buttons/custom_filled_button_widget.dart';
import 'package:mides_skadik/widgets/components/custom_container.dart';
import 'package:mides_skadik/widgets/components/custom_scaffold.dart';
import 'package:mides_skadik/widgets/course/class/custom_filter_course.dart';
import 'package:mides_skadik/widgets/course/custom_appbar_course.dart';
import 'package:mides_skadik/widgets/course/custom_assignment_course.dart';
import 'package:mides_skadik/widgets/course/custom_history_assignment_course.dart';
import 'package:mides_skadik/widgets/course/custom_materi_pendukung.dart';
import 'package:mides_skadik/widgets/custom_text_field_widget.dart';
import 'package:mides_skadik/widgets/custom_text_wigdet.dart';
import 'package:mides_skadik/widgets/course/class/custom_card_tasks_course.dart';
import '../controllers/clases_course_controller.dart';

class ClasesCourseView extends GetView<ClasesCourseController> {
  const ClasesCourseView({super.key});

  @override
  Widget build(BuildContext context) {
    HanjarController hanjar = Get.put(HanjarController());

    return SafeArea(
      child: CustomScaffold(
        body: Stack(
          children: [
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  90.verticalSpace,
                  Padding(
                    padding:
                        EdgeInsets.symmetric(vertical: 60.h, horizontal: 60.w),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        CustomTextWigdet(
                          title: 'My Clases',
                          fontSize: 32,
                          fontWeight: FontWeight.w700,
                          textColor: whiteColor,
                        ),
                        40.verticalSpace,
                        Obx(
                          () => Row(
                            children: [
                              CustomFilledButtonWidget(
                                onPressed: () {
                                  controller.selectTab(0);
                                  hanjar.fetchMapel();
                                },
                                title: 'Course',
                                fontColor: secondWhiteColor,
                                fontSize: 18,
                                fontWeight: FontWeight.w400,
                                bgColor:
                                    controller.selectedTabClasesIndex.value == 0
                                        ? baseBlueColor
                                        : whiteColor.withOpacity(0.05),
                                widthButton: 109,
                                heightButton: 48,
                                radius: 8,
                              ),
                              8.horizontalSpace,
                              CustomFilledButtonWidget(
                                onPressed: () => controller.selectTab(1),
                                title: 'Quiz',
                                fontColor: secondWhiteColor,
                                fontSize: 18,
                                fontWeight: FontWeight.w400,
                                bgColor:
                                    controller.selectedTabClasesIndex.value == 1
                                        ? baseBlueColor
                                        : whiteColor.withOpacity(0.05),
                                widthButton: 87,
                                heightButton: 48,
                                radius: 8,
                              ),
                              8.horizontalSpace,
                              CustomFilledButtonWidget(
                                onPressed: () => controller.selectTab(2),
                                title: 'Assignment',
                                fontColor: secondWhiteColor,
                                fontSize: 18,
                                fontWeight: FontWeight.w400,
                                bgColor:
                                    controller.selectedTabClasesIndex.value == 2
                                        ? baseBlueColor
                                        : whiteColor.withOpacity(0.05),
                                widthButton: 148,
                                heightButton: 48,
                                radius: 8,
                              ),
                            ],
                          ),
                        ),
                        40.verticalSpace,
                        Obx(
                          () => Visibility(
                            visible:
                                controller.selectedTabClasesIndex.value == 2
                                    ? true
                                    : false,
                            child: SizedBox(
                              child: Column(
                                children: [
                                  Row(
                                    children: [
                                      InkWell(
                                        onTap: () {
                                          controller.selectedMapet(0);
                                          controller.searchController.clear();
                                        },
                                        child: const CustomContainer(
                                          widget: CustomTextWigdet(
                                              title: "All Task List"),
                                        ),
                                      ),
                                      const SizedBox(
                                        width: 32,
                                      ),
                                      InkWell(
                                        onTap: () {
                                          controller.selectedMapet(1);
                                          controller.searchController.clear();
                                          controller.getHistoryAssignment();
                                        },
                                        child: const CustomContainer(
                                          widget: CustomTextWigdet(
                                              title: "Task Histories"),
                                        ),
                                      ),
                                    ],
                                  ),
                                  const SizedBox(
                                    height: 10,
                                  ),
                                  Obx(
                                    () => Container(
                                      width: Get.width,
                                      height: 0.5,
                                      color: greyColor,
                                      child: Row(
                                        children: [
                                          Container(
                                            width: Get.width * 0.2,
                                            height: 0.5,
                                            margin: EdgeInsets.only(
                                                right: Get.width * 0.04),
                                            color: controller.selectedMapetIndex
                                                        .value ==
                                                    0
                                                ? blueColor
                                                : greyColor.withOpacity(0),
                                          ),
                                          Container(
                                            width: Get.width * 0.14,
                                            height: 1,
                                            color: controller.selectedMapetIndex
                                                        .value ==
                                                    1
                                                ? blueColor
                                                : greyColor.withOpacity(0),
                                          ),
                                        ],
                                      ),
                                    ),
                                  )
                                ],
                              ),
                            ),
                          ),
                        ),
                        Obx(
                          () => Visibility(
                            visible:
                                controller.selectedTabClasesIndex.value == 0
                                    ? true
                                    : false,
                            child: Container(
                              child: Column(
                                children: [
                                  Container(
                                    width: Get.width,
                                    child: SingleChildScrollView(
                                      scrollDirection: Axis.horizontal,
                                      child: Row(
                                        children:
                                            hanjar.mapel.asMap().entries.map(
                                          (e) {
                                            final index = e.key;
                                            final value = e.value;

                                            if (index == 0) {
                                              Future.delayed(Duration.zero, () {
                                                hanjar.fetchSubMapel(
                                                    id: value.id.toString());
                                              });
                                            }

                                            return Container(
                                              width: Get.width * 0.18,
                                              padding:
                                                  const EdgeInsets.symmetric(
                                                      horizontal: 10),
                                              child: InkWell(
                                                onTap: () {
                                                  controller.selectSbs(index);
                                                  hanjar.subMapel.clear();
                                                  hanjar.filteredAssignment
                                                      .clear();
                                                  hanjar.fetchSubMapel(
                                                      id: value.id.toString());
                                                },
                                                child: Column(
                                                  children: [
                                                    Text(
                                                      value.name ?? "",
                                                      style: TextStyle(
                                                          fontSize: 18.sp,
                                                          fontWeight:
                                                              FontWeight.w600,
                                                          color: whiteColor),
                                                    ),
                                                    const SizedBox(
                                                      height: 5,
                                                    ),
                                                    Container(
                                                      padding: const EdgeInsets
                                                          .symmetric(
                                                          horizontal: 10),
                                                      height: 1,
                                                      color: controller
                                                                  .selectSbs
                                                                  .value ==
                                                              index
                                                          ? blueColor
                                                          : Colors.transparent,
                                                    )
                                                  ],
                                                ),
                                              ),
                                            );
                                          },
                                        ).toList(),
                                      ),
                                    ),
                                  ),
                                  const SizedBox(
                                    height: 10,
                                  ),
                                ],
                              ),
                            ),
                          ),
                        ),
                        Obx(
                          () => Visibility(
                              visible:
                                  controller.selectedTabClasesIndex.value == 1
                                      ? false
                                      : true,
                              child: 40.verticalSpace),
                        ),
                        Obx(() {
                          return controller.selectedTabClasesIndex.value == 0
                              ? Row(
                                  children: [
                                    CustomFilledButtonWidget(
                                      onPressed: () {
                                        controller.openFilter();
                                      },
                                      withIcon: true,
                                      assetName: 'assets/icons/filter.svg',
                                      widthIcon: 24,
                                      heightIcon: 24,
                                      title: 'Add Filter',
                                      fontSize: 18,
                                      fontWeight: FontWeight.w400,
                                      fontColor: whiteColor,
                                      bgColor: whiteColor.withOpacity(0.05),
                                      widthButton: 160,
                                      heightButton: 48,
                                      radius: 8,
                                    ),
                                    8.horizontalSpace,
                                    CustomTextFieldWidget(
                                      controller: hanjar.searchController,
                                      onChanged: (value) =>
                                          hanjar.searchText.value = value,
                                      radius: 8,
                                      colorField: whiteColor.withOpacity(0.05),
                                      colorText: whiteColor,
                                      hintText: 'Search',
                                      fontSize: 18,
                                      fontWeight: FontWeight.w400,
                                      colorTextHint:
                                          whiteColor.withOpacity(0.5),
                                      assetNameIcon: 'assets/icons/search.svg',
                                      iconWidth: 5,
                                      iconHeight: 5,
                                      widthField: 240,
                                      heightField: 48,
                                      contentPadding:
                                          EdgeInsetsDirectional.only(
                                        top: 16.h,
                                      ),
                                    ),
                                  ],
                                )
                              : controller.selectedTabClasesIndex.value == 1
                                  ? Row(
                                      children: [
                                        CustomFilledButtonWidget(
                                          onPressed: () {
                                            controller.openFilter();
                                          },
                                          withIcon: true,
                                          assetName: 'assets/icons/filter.svg',
                                          widthIcon: 24,
                                          heightIcon: 24,
                                          title: 'Add Filter',
                                          fontSize: 18,
                                          fontWeight: FontWeight.w400,
                                          fontColor: whiteColor,
                                          bgColor: whiteColor.withOpacity(0.05),
                                          widthButton: 160,
                                          heightButton: 48,
                                          radius: 8,
                                        ),
                                        8.horizontalSpace,
                                        CustomTextFieldWidget(
                                          radius: 8,
                                          colorField:
                                              whiteColor.withOpacity(0.05),
                                          colorText: whiteColor,
                                          hintText: 'Search',
                                          fontSize: 18,
                                          fontWeight: FontWeight.w400,
                                          colorTextHint:
                                              whiteColor.withOpacity(0.5),
                                          assetNameIcon:
                                              'assets/icons/search.svg',
                                          iconWidth: 5,
                                          iconHeight: 5,
                                          widthField: 240,
                                          heightField: 48,
                                          contentPadding:
                                              EdgeInsetsDirectional.only(
                                            top: 16.h,
                                          ),
                                        ),
                                      ],
                                    )
                                  : controller.selectedTabClasesIndex.value == 2
                                      ? Row(
                                          children: [
                                            CustomFilledButtonWidget(
                                              onPressed: () {
                                                controller.openFilter();
                                              },
                                              withIcon: true,
                                              assetName:
                                                  'assets/icons/time.svg',
                                              widthIcon: 24,
                                              heightIcon: 24,
                                              title: 'Time',
                                              fontSize: 18,
                                              fontWeight: FontWeight.w400,
                                              fontColor: whiteColor,
                                              bgColor:
                                                  whiteColor.withOpacity(0.05),
                                              widthButton: 139,
                                              heightButton: 48,
                                              radius: 8,
                                            ),
                                            8.horizontalSpace,
                                            CustomFilledButtonWidget(
                                              onPressed: () {
                                                controller.openFilter();
                                              },
                                              withIcon: true,
                                              assetName:
                                                  'assets/icons/calendar.svg',
                                              widthIcon: 24,
                                              heightIcon: 24,
                                              title: 'Deadline',
                                              fontSize: 18,
                                              fontWeight: FontWeight.w400,
                                              fontColor: whiteColor,
                                              bgColor:
                                                  whiteColor.withOpacity(0.05),
                                              widthButton: 160,
                                              heightButton: 48,
                                              radius: 8,
                                            ),
                                            8.horizontalSpace,
                                            CustomTextFieldWidget(
                                              controller:
                                                  controller.searchController,
                                              onChanged: (value) => controller
                                                  .searchText.value = value,
                                              radius: 8,
                                              colorField:
                                                  whiteColor.withOpacity(0.05),
                                              colorText: whiteColor,
                                              hintText: 'Search',
                                              fontSize: 18,
                                              fontWeight: FontWeight.w400,
                                              colorTextHint:
                                                  whiteColor.withOpacity(0.5),
                                              assetNameIcon:
                                                  'assets/icons/search.svg',
                                              iconWidth: 5,
                                              iconHeight: 5,
                                              widthField: 240,
                                              heightField: 48,
                                              contentPadding:
                                                  EdgeInsetsDirectional.only(
                                                top: 16.h,
                                              ),
                                            ),
                                          ],
                                        )
                                      : SizedBox();
                        }),
                        40.verticalSpace,
                        Obx(() {
                          if (controller.selectedTabClasesIndex.value == 0) {
                            // if (controller.selectedMapetIndex.value == 0) {
                            return Obx(
                              () => SizedBox(
                                  height: Get.height / 1.5,
                                  child: ListView(
                                    children: [
                                      Wrap(
                                        spacing: 20.w,
                                        runSpacing: 20.h,
                                        children: [
                                          if (!hanjar.isLoadingSubmapel.value &&
                                              hanjar.filteredAssignment.isEmpty)
                                            const Center(
                                              child: CustomTextWigdet(
                                                title: 'No Course',
                                                fontSize: 32,
                                                fontWeight: FontWeight.w700,
                                                textColor: Colors.white,
                                              ),
                                            ),
                                          ...hanjar.filteredAssignment.map((e) {
                                            return CustomMateriPendukung(
                                              titleQuiz: e.name ?? '',
                                              subjectName:
                                                  e.createdBy!.name ?? '',
                                              totalQuestion:
                                                  e.count!.assignments ?? 0,
                                              onPressed: () {
                                                // controller.attemptQuiz(
                                                //   id: e.id,
                                                // );
                                              },
                                            );
                                          }),
                                          if (controller.isLoading.value)
                                            const Center(
                                              child: CircularProgressIndicator(
                                                color: Colors.white,
                                              ),
                                            ),
                                          if (controller.todalData.value !=
                                              controller.listQuiz.length)
                                            CustomFilledButtonWidget(
                                              onPressed: () {
                                                // controller.fetchMoreData();
                                              },
                                              title: 'Load More',
                                              fontColor: secondWhiteColor,
                                              fontSize: 18,
                                              fontWeight: FontWeight.w400,
                                              bgColor:
                                                  whiteColor.withOpacity(0.05),
                                              widthButton: 139,
                                              heightButton: 48,
                                              radius: 8,
                                            ),
                                        ],
                                      ),
                                    ],
                                  )),
                            );
                            // } else if (controller.selectedMapetIndex.value ==
                            //     0) {
                            //   return SizedBox(
                            //     height: 1500.h,
                            //     child: SingleChildScrollView(
                            //         child: Column(
                            //       children: List.generate(10, (index) {
                            //         return CustomHistoryAssignmentCourse();
                            //       }),
                            //     )),
                            //   );
                            // } else {
                            //   return SizedBox(
                            //     height: 1500.h,
                            //     child: SingleChildScrollView(
                            //         child: Column(
                            //       children: List.generate(10, (index) {
                            //         return CustomHistoryAssignmentCourse();
                            //       }),
                            //     )),
                            //   );
                            // }
                          } else if (controller.selectedTabClasesIndex.value ==
                              1) {
                            // Quiz
                            return SizedBox(
                                height: Get.height / 1.5,
                                child: ListView(
                                  children: [
                                    Wrap(
                                      spacing: 20.w,
                                      runSpacing: 20.h,
                                      children: [
                                        if (!controller.isLoading.value &&
                                            controller.listQuiz.isEmpty)
                                          const Center(
                                            child: CustomTextWigdet(
                                              title: 'No Quiz Found',
                                              fontSize: 32,
                                              fontWeight: FontWeight.w700,
                                              textColor: Colors.white,
                                            ),
                                          ),
                                        ...controller.listQuiz.map((e) {
                                          return CustomCardTasksCourse(
                                            title: e.title ?? "",
                                            subStudies: e.submapel?.mapel?.name,
                                            subjectName: e.submapel?.name,
                                            totalQuestion:
                                                e.count?.quizQuestions,
                                            timeAssigned: getTimeDifference(
                                                e.startDate ?? DateTime.now()),
                                            date: e.startDate ?? DateTime.now(),
                                            onPressed: () {
                                              if (!(e.startDate != null &&
                                                  DateTime.now()
                                                      .isAfter(e.startDate!))) {
                                                controller.attemptQuiz(
                                                  id: e.id,
                                                );
                                              }
                                            },
                                          );
                                        }),
                                        if (controller.isLoading.value)
                                          const Center(
                                            child: CircularProgressIndicator(
                                              color: Colors.white,
                                            ),
                                          ),
                                        if (controller.todalData.value !=
                                            controller.listQuiz.length)
                                          CustomFilledButtonWidget(
                                            onPressed: () {
                                              controller.fetchMoreData();
                                            },
                                            title: 'Load More',
                                            fontColor: secondWhiteColor,
                                            fontSize: 18,
                                            fontWeight: FontWeight.w400,
                                            bgColor:
                                                whiteColor.withOpacity(0.05),
                                            widthButton: 139,
                                            heightButton: 48,
                                            radius: 8,
                                          ),
                                      ],
                                    ),
                                  ],
                                ));
                          } else if (controller.selectedTabClasesIndex.value ==
                              2) {
                            if (controller.selectedMapetIndex.value == 0) {
                              return SizedBox(
                                height: Get.height / 1.5,
                                child: Obx(
                                  () => ListView(
                                    children: [
                                      if (!controller.isLoading.value &&
                                          controller.listAssigment.isEmpty)
                                        const Center(
                                          child: CustomTextWigdet(
                                            title: 'No Assignment Found',
                                            fontSize: 32,
                                            fontWeight: FontWeight.w700,
                                            textColor: Colors.white,
                                          ),
                                        ),
                                      ...controller.filteredAssignment.map((e) {
                                        return CustomAssignmentCourse(
                                          isSubmited: false,
                                          titleAssignment: e.title,
                                          lectureName: e.createdBy?.name,
                                          mapel: e.subMapel?.name.toString(),
                                          timeRemaining:
                                              e.timeRemaining.toString(),
                                          onTap: () {
                                            Get.to(
                                              DetailAssignmentView(
                                                index: controller
                                                    .selectedMapetIndex.value,
                                              ),
                                              arguments: {'id': '${e.id}'},
                                            );
                                          },
                                        );
                                      }),
                                      if (controller.isLoading.value)
                                        const Center(
                                          child: CircularProgressIndicator(
                                            color: Colors.white,
                                          ),
                                        ),
                                      if (controller.todalData.value !=
                                          controller.listQuiz.length)
                                        CustomFilledButtonWidget(
                                          onPressed: () {
                                            controller.fetchMoreData();
                                          },
                                          title: 'Load More',
                                          fontColor: secondWhiteColor,
                                          fontSize: 18,
                                          fontWeight: FontWeight.w400,
                                          bgColor: whiteColor.withOpacity(0.05),
                                          widthButton: 139,
                                          heightButton: 48,
                                          radius: 8,
                                        ),
                                    ],
                                  ),
                                ),
                              );
                            } else {
                              return Obx(
                                () => SizedBox(
                                    height: Get.height / 1.5,
                                    child: ListView(
                                      children: [
                                        if (!controller.isLoading.value &&
                                            controller.filteredHistoryAssignment
                                                .isEmpty)
                                          const Center(
                                            child: CustomTextWigdet(
                                              title: 'No Assignment Found',
                                              fontSize: 32,
                                              fontWeight: FontWeight.w700,
                                              textColor: Colors.white,
                                            ),
                                          ),
                                        ...controller.filteredHistoryAssignment
                                            .map((e) {
                                          return CustomAssignmentCourse(
                                            titleAssignment: e.title,
                                            lectureName: e.createdBy?.name,
                                            mapel: e.subMapel?.name.toString(),
                                            timeRemaining:
                                                e.timeRemaining.toString(),
                                            onTap: () {
                                              print(e.id);

                                              Get.to(
                                                DetailAssignmentView(
                                                  index: controller
                                                      .selectedMapetIndex.value,
                                                ),
                                                arguments: {'id': '${e.id}'},
                                              );
                                            },
                                          );
                                        }),
                                        if (controller.isLoading.value)
                                          const Center(
                                            child: CircularProgressIndicator(
                                              color: Colors.white,
                                            ),
                                          ),
                                        if (controller.todalData.value !=
                                            controller.listQuiz.length)
                                          CustomFilledButtonWidget(
                                            onPressed: () {
                                              controller.fetchMoreData();
                                            },
                                            title: 'Load More',
                                            fontColor: secondWhiteColor,
                                            fontSize: 18,
                                            fontWeight: FontWeight.w400,
                                            bgColor:
                                                whiteColor.withOpacity(0.05),
                                            widthButton: 139,
                                            heightButton: 48,
                                            radius: 8,
                                          ),
                                      ],
                                    )),
                              );
                            }
                          } else {
                            return SizedBox(
                              height: 1600.h,
                              child: SingleChildScrollView(
                                child: Wrap(
                                  spacing: 20.w,
                                  runSpacing: 20.h,
                                  children: List.generate(10, (index) {
                                    // return CustomQuizCourse();/
                                    return SizedBox();
                                  }),
                                ),
                              ),
                            );
                          }
                        }),
                      ],
                    ),
                  ),
                ],
              ),
            ),
            Positioned(
              top: 465.h,
              left: 60.w,
              child: Obx(() => controller.isOpen.value
                  ? CustomFilterCourse(
                      onPressedTime: (p0) {
                        controller.isOpen.value = false;
                        controller.selectedFilterTime.value = p0;
                        controller.filterAssignment();
                      },
                      selectedTime: controller.selectedFilterTime.value,
                      onPressedDeadline: (p0) {
                        controller.isOpen.value = false;
                        controller.selectedFilterDeadline.value = p0;
                        controller.filterAssignment();
                      },
                      selectedDeadline: controller.selectedFilterDeadline.value,
                      onPressedReset: () {
                        controller.isOpen.value = false;
                        controller.selectedFilterTime.value = '';
                        controller.selectedFilterDeadline.value = '';
                        controller.filterAssignment();
                      },
                    )
                  : const SizedBox()),
            ),
            const CustomAppBarCourse(height: 60),
          ],
        ),
      ),
    );
  }
}
